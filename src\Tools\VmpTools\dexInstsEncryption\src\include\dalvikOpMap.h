// auto generate header
#ifndef DALVIK_OPMAP_H_
#define DALVIK_OPMAP_H_
const static unsigned char origOpToNewOp[256] = {0x31, 0x58, 0x69, 0x19, 0x9e, 0xa3, 0xc4, 0xad, 0x52, 0x30, 0x86, 0xba, 0x5f, 0xe0, 0x42, 0x38, 0xef, 0x6f, 0x55, 0x97, 0x70, 0x5c, 0x78, 0x85, 0x77, 0x06, 0x89, 0xd5, 0xd0, 0x5b, 0xcf, 0xb8, 0x40, 0xed, 0x02, 0x80, 0xb1, 0x27, 0x92, 0xc5, 0xd9, 0xf0, 0x2a, 0x54, 0xe6, 0x63, 0xd2, 0xc2, 0x23, 0x34, 0x62, 0x33, 0xe5, 0xbf, 0xdf, 0x1b, 0x5d, 0x6b, 0x4e, 0x46, 0xa0, 0x5e, 0x22, 0x8e, 0xa7, 0xeb, 0x29, 0xe3, 0xac, 0xbd, 0x1f, 0xaa, 0x20, 0xe8, 0x28, 0x21, 0x0e, 0x11, 0xa9, 0x4a, 0xf8, 0x60, 0xe1, 0x67, 0xb4, 0xf2, 0x82, 0xbc, 0x98, 0x0a, 0x04, 0x75, 0x9c, 0xf9, 0xf1, 0x2e, 0x10, 0x4c, 0x5a, 0x8a, 0xb3, 0x9a, 0xd3, 0xa6, 0x7a, 0xa5, 0x94, 0x3e, 0xde, 0xcc, 0x59, 0x56, 0x18, 0x84, 0x37, 0x32, 0xa1, 0xec, 0xb2, 0xc6, 0x1d, 0x72, 0xc3, 0x57, 0x83, 0xa8, 0x8b, 0x79, 0xab, 0xcb, 0xf7, 0xc0, 0x14, 0x96, 0x2d, 0x6c, 0xbb, 0x88, 0xc1, 0x12, 0x65, 0x3c, 0x50, 0xe7, 0x00, 0x35, 0xf4, 0xf5, 0x1e, 0x99, 0x73, 0x81, 0x6e, 0x3f, 0x26, 0xb9, 0x41, 0xe9, 0x01, 0x0c, 0x6d, 0x7c, 0x9d, 0x4b, 0x43, 0x0f, 0xf3, 0xfe, 0x90, 0x39, 0x8f, 0x7b, 0x9f, 0xaf, 0x64, 0x95, 0xee, 0x66, 0xfc, 0xb5, 0x48, 0x4f, 0x07, 0x17, 0x49, 0x13, 0xc8, 0x51, 0xff, 0x76, 0xd1, 0x61, 0x15, 0xc7, 0xfd, 0xc9, 0xcd, 0x25, 0x44, 0x3d, 0x0d, 0x8c, 0xb6, 0x7f, 0x24, 0x1a, 0x91, 0xd8, 0x3a, 0x09, 0xae, 0xe4, 0x74, 0xea, 0xdc, 0xa4, 0xe2, 0x7d, 0xdd, 0xfb, 0xca, 0xd6, 0x2f, 0x71, 0xdb, 0x8d, 0xa2, 0x05, 0x87, 0x2c, 0x45, 0x08, 0xbe, 0x36, 0x2b, 0xd4, 0x0b, 0x9b, 0x03, 0xfa, 0x7e, 0xd7, 0xb7, 0xf6, 0x4d, 0x93, 0xb0, 0xce, 0x47, 0x1c, 0xda, 0x6a, 0x3b, 0x68, 0x53, 0x16};
#endif
