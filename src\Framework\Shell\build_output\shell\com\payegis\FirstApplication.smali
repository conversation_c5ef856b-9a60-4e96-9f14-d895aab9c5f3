.class public Lcom/payegis/FirstApplication;
.super Landroid/app/Application;
.source "FirstApplication.java"


# static fields
.field private static app:Landroid/app/Application;

.field private static context:Landroid/content/Context;

.field private static shellApp:Landroid/app/Application;

.field private static strEntryApplication:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .registers 2

    .prologue
    const/4 v0, 0x0

    .line 22
    sput-object v0, Lcom/payegis/FirstApplication;->app:Landroid/app/Application;

    .line 23
    sput-object v0, Lcom/payegis/FirstApplication;->shellApp:Landroid/app/Application;

    .line 25
    const-string v0, "com.payegis.entry"

    sput-object v0, Lcom/payegis/FirstApplication;->strEntryApplication:Ljava/lang/String;

    .line 29
    :try_start_9
    sget-object v0, Landroid/os/Build;->SUPPORTED_ABIS:[Ljava/lang/String;

    invoke-static {v0}, Ljava/util/Arrays;->toString([Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    const-string v1, "x86"

    invoke-virtual {v0, v1}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_1d

    .line 31
    const-string v0, "egis-x86"

    invoke-static {v0}, Ljava/lang/System;->loadLibrary(Ljava/lang/String;)V

    .line 42
    :goto_1c
    return-void

    .line 34
    :cond_1d
    const-string v0, "egis"

    invoke-static {v0}, Ljava/lang/System;->loadLibrary(Ljava/lang/String;)V
    :try_end_22
    .catch Ljava/lang/Exception; {:try_start_9 .. :try_end_22} :catch_23

    goto :goto_1c

    .line 37
    :catch_23
    move-exception v0

    .line 39
    const-string v1, "egis"

    invoke-static {v1}, Ljava/lang/System;->loadLibrary(Ljava/lang/String;)V

    .line 40
    invoke-virtual {v0}, Ljava/lang/Exception;->printStackTrace()V

    goto :goto_1c
.end method

.method public constructor <init>()V
    .registers 1

    .prologue
    .line 45
    invoke-direct {p0}, Landroid/app/Application;-><init>()V

    .line 46
    return-void
.end method

.method private native attachbasecontext(Landroid/content/Context;)V
.end method

.method private checkMainProcess(Landroid/content/Context;Ljava/lang/String;)Z
    .registers 4

    .prologue
    .line 78
    if-nez p2, :cond_4

    const/4 v0, 0x1

    .line 79
    :goto_3
    return v0

    :cond_4
    invoke-virtual {p1}, Landroid/content/Context;->getPackageName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p2, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    goto :goto_3
.end method

.method public static getAppContext()Landroid/content/Context;
    .registers 1

    .prologue
    .line 49
    sget-object v0, Lcom/payegis/FirstApplication;->context:Landroid/content/Context;

    return-object v0
.end method

.method private getProcessName(Landroid/content/Context;)Ljava/lang/String;
    .registers 6

    .prologue
    .line 57
    :try_start_0
    const-string v0, "activity"

    invoke-virtual {p1, v0}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/app/ActivityManager;

    .line 58
    if-eqz v0, :cond_33

    .line 59
    invoke-virtual {v0}, Landroid/app/ActivityManager;->getRunningAppProcesses()Ljava/util/List;

    move-result-object v0

    .line 60
    if-eqz v0, :cond_33

    .line 61
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_14
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_33

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/app/ActivityManager$RunningAppProcessInfo;

    .line 62
    iget v2, v0, Landroid/app/ActivityManager$RunningAppProcessInfo;->pid:I

    invoke-static {}, Landroid/os/Process;->myPid()I

    move-result v3

    if-ne v2, v3, :cond_14

    .line 63
    iget-object v0, v0, Landroid/app/ActivityManager$RunningAppProcessInfo;->processName:Ljava/lang/String;
    :try_end_2a
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_2a} :catch_2b

    .line 71
    :goto_2a
    return-object v0

    .line 68
    :catch_2b
    move-exception v0

    .line 69
    const-string v1, "FirstApplication"

    const-string v2, "Failed to get process name"

    invoke-static {v1, v2, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I

    .line 71
    :cond_33
    const/4 v0, 0x0

    goto :goto_2a
.end method

.method public static installProvider(Landroid/app/Application;)V
    .registers 8

    .prologue
    .line 162
    :try_start_0
    const-string v0, "android.app.ActivityThread"

    invoke-static {v0}, Ljava/lang/Class;->forName(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v1

    .line 163
    const-string v0, "currentActivityThread"

    const/4 v2, 0x0

    new-array v2, v2, [Ljava/lang/Class;

    invoke-virtual {v1, v0, v2}, Ljava/lang/Class;->getMethod(Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;

    move-result-object v0

    const/4 v2, 0x0

    const/4 v3, 0x0

    new-array v3, v3, [Ljava/lang/Object;

    invoke-virtual {v0, v2, v3}, Ljava/lang/reflect/Method;->invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    .line 164
    const-string v0, "mBoundApplication"

    invoke-virtual {v1, v0}, Ljava/lang/Class;->getDeclaredField(Ljava/lang/String;)Ljava/lang/reflect/Field;

    move-result-object v0

    .line 165
    const/4 v3, 0x1

    invoke-virtual {v0, v3}, Ljava/lang/reflect/Field;->setAccessible(Z)V

    .line 166
    invoke-virtual {v0, v2}, Ljava/lang/reflect/Field;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    .line 167
    const-string v3, "android.app.ActivityThread$AppBindData"

    invoke-static {v3}, Ljava/lang/Class;->forName(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v3

    .line 168
    const-string v4, "providers"

    invoke-virtual {v3, v4}, Ljava/lang/Class;->getDeclaredField(Ljava/lang/String;)Ljava/lang/reflect/Field;

    move-result-object v3

    .line 169
    const/4 v4, 0x1

    invoke-virtual {v3, v4}, Ljava/lang/reflect/Field;->setAccessible(Z)V

    .line 170
    invoke-virtual {v3, v0}, Ljava/lang/reflect/Field;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/List;

    .line 171
    if-eqz v0, :cond_63

    .line 172
    const-string v3, "installContentProviders"

    const/4 v4, 0x2

    new-array v4, v4, [Ljava/lang/Class;

    const/4 v5, 0x0

    const-class v6, Landroid/content/Context;

    aput-object v6, v4, v5

    const/4 v5, 0x1

    const-class v6, Ljava/util/List;

    aput-object v6, v4, v5

    invoke-virtual {v1, v3, v4}, Ljava/lang/Class;->getDeclaredMethod(Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;

    move-result-object v1

    .line 173
    const/4 v3, 0x1

    invoke-virtual {v1, v3}, Ljava/lang/reflect/Method;->setAccessible(Z)V

    .line 174
    const/4 v3, 0x2

    new-array v3, v3, [Ljava/lang/Object;

    const/4 v4, 0x0

    aput-object p0, v3, v4

    const/4 v4, 0x1

    aput-object v0, v3, v4

    invoke-virtual {v1, v2, v3}, Ljava/lang/reflect/Method;->invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    .line 175
    invoke-interface {v0}, Ljava/util/List;->clear()V
    :try_end_63
    .catch Ljava/lang/Throwable; {:try_start_0 .. :try_end_63} :catch_64

    .line 180
    :cond_63
    :goto_63
    return-void

    .line 178
    :catch_64
    move-exception v0

    goto :goto_63
.end method

.method public static native interfaceMC()V
.end method

.method public static native interfaceR(Landroid/app/Application;Landroid/content/Context;I)V
.end method

.method public static native interfaceRS()V
.end method

.method public static native interfaceV(I)V
.end method

.method private native oncreate(Landroid/app/Application;Landroid/content/Context;)V
.end method


# virtual methods
.method protected attachBaseContext(Landroid/content/Context;)V
    .registers 9

    .prologue
    const/4 v6, 0x0

    const/4 v5, 0x1

    .line 99
    invoke-super {p0, p1}, Landroid/app/Application;->attachBaseContext(Landroid/content/Context;)V

    .line 100
    sput-object p1, Lcom/payegis/FirstApplication;->context:Landroid/content/Context;

    .line 103
    invoke-direct {p0, p1}, Lcom/payegis/FirstApplication;->getProcessName(Landroid/content/Context;)Ljava/lang/String;

    move-result-object v0

    .line 104
    invoke-direct {p0, p1, v0}, Lcom/payegis/FirstApplication;->checkMainProcess(Landroid/content/Context;Ljava/lang/String;)Z

    move-result v1

    .line 106
    const-string v2, "FirstApplication"

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Process: "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v4, ", isMain: "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-static {v2, v3}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 109
    invoke-direct {p0, p1}, Lcom/payegis/FirstApplication;->attachbasecontext(Landroid/content/Context;)V

    .line 112
    if-eqz v1, :cond_81

    .line 113
    invoke-virtual {p0, p1}, Lcom/payegis/FirstApplication;->getNewAppInstance(Landroid/content/Context;)Landroid/app/Application;

    move-result-object v0

    sput-object v0, Lcom/payegis/FirstApplication;->app:Landroid/app/Application;

    .line 114
    sget-object v0, Lcom/payegis/FirstApplication;->shellApp:Landroid/app/Application;

    if-nez v0, :cond_42

    .line 115
    sput-object p0, Lcom/payegis/FirstApplication;->shellApp:Landroid/app/Application;

    .line 116
    :cond_42
    sget-object v0, Lcom/payegis/FirstApplication;->app:Landroid/app/Application;

    if-eqz v0, :cond_7d

    .line 118
    :try_start_46
    const-class v0, Landroid/app/Application;

    const-string v1, "attach"

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Class;

    const/4 v3, 0x0

    const-class v4, Landroid/content/Context;

    aput-object v4, v2, v3

    invoke-virtual {v0, v1, v2}, Ljava/lang/Class;->getDeclaredMethod(Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;

    move-result-object v0

    .line 119
    if-eqz v0, :cond_67

    .line 120
    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Ljava/lang/reflect/Method;->setAccessible(Z)V

    .line 121
    sget-object v1, Lcom/payegis/FirstApplication;->app:Landroid/app/Application;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p1, v2, v3

    invoke-virtual {v0, v1, v2}, Ljava/lang/reflect/Method;->invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;
    :try_end_67
    .catch Ljava/lang/Exception; {:try_start_46 .. :try_end_67} :catch_78

    .line 126
    :cond_67
    :goto_67
    sget-object v0, Lcom/payegis/FirstApplication;->app:Landroid/app/Application;

    sget-object v1, Lcom/payegis/FirstApplication;->shellApp:Landroid/app/Application;

    invoke-virtual {v1}, Landroid/app/Application;->getBaseContext()Landroid/content/Context;

    move-result-object v1

    invoke-static {v0, v1, v6}, Lcom/payegis/FirstApplication;->interfaceR(Landroid/app/Application;Landroid/content/Context;I)V

    .line 127
    sget-object v0, Lcom/payegis/FirstApplication;->app:Landroid/app/Application;

    invoke-static {v0}, Lcom/payegis/FirstApplication;->installProvider(Landroid/app/Application;)V

    .line 137
    :cond_77
    :goto_77
    return-void

    .line 123
    :catch_78
    move-exception v0

    .line 124
    invoke-virtual {v0}, Ljava/lang/Exception;->printStackTrace()V

    goto :goto_67

    .line 129
    :cond_7d
    invoke-static {v5}, Ljava/lang/System;->exit(I)V

    goto :goto_77

    .line 133
    :cond_81
    const-string v1, "FirstApplication"

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Non-main process DEX decryption completed: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 134
    sget-object v0, Lcom/payegis/FirstApplication;->shellApp:Landroid/app/Application;

    if-nez v0, :cond_77

    .line 135
    sput-object p0, Lcom/payegis/FirstApplication;->shellApp:Landroid/app/Application;

    goto :goto_77
.end method

.method public getNewAppInstance(Landroid/content/Context;)Landroid/app/Application;
    .registers 4

    .prologue
    .line 84
    :try_start_0
    sget-object v0, Lcom/payegis/FirstApplication;->app:Landroid/app/Application;

    if-nez v0, :cond_1a

    .line 85
    invoke-virtual {p1}, Landroid/content/Context;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object v0

    .line 86
    if-eqz v0, :cond_1a

    .line 87
    sget-object v1, Lcom/payegis/FirstApplication;->strEntryApplication:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/ClassLoader;->loadClass(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v0

    .line 88
    if-eqz v0, :cond_1a

    .line 89
    invoke-virtual {v0}, Ljava/lang/Class;->newInstance()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/app/Application;

    sput-object v0, Lcom/payegis/FirstApplication;->app:Landroid/app/Application;
    :try_end_1a
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_1a} :catch_1d

    .line 95
    :cond_1a
    :goto_1a
    sget-object v0, Lcom/payegis/FirstApplication;->app:Landroid/app/Application;

    return-object v0

    .line 92
    :catch_1d
    move-exception v0

    .line 93
    invoke-virtual {v0}, Ljava/lang/Exception;->printStackTrace()V

    goto :goto_1a
.end method

.method public onCreate()V
    .registers 5

    .prologue
    .line 142
    invoke-super {p0}, Landroid/app/Application;->onCreate()V

    .line 145
    invoke-direct {p0, p0}, Lcom/payegis/FirstApplication;->getProcessName(Landroid/content/Context;)Ljava/lang/String;

    move-result-object v0

    .line 146
    invoke-direct {p0, p0, v0}, Lcom/payegis/FirstApplication;->checkMainProcess(Landroid/content/Context;Ljava/lang/String;)Z

    move-result v1

    .line 148
    if-eqz v1, :cond_1d

    sget-object v1, Lcom/payegis/FirstApplication;->app:Landroid/app/Application;

    if-eqz v1, :cond_1d

    .line 150
    sget-object v0, Lcom/payegis/FirstApplication;->app:Landroid/app/Application;

    sget-object v1, Lcom/payegis/FirstApplication;->shellApp:Landroid/app/Application;

    invoke-virtual {v1}, Landroid/app/Application;->getBaseContext()Landroid/content/Context;

    move-result-object v1

    invoke-direct {p0, v0, v1}, Lcom/payegis/FirstApplication;->oncreate(Landroid/app/Application;Landroid/content/Context;)V

    .line 156
    :goto_1c
    return-void

    .line 153
    :cond_1d
    const-string v1, "FirstApplication"

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Non-main process onCreate: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 154
    const/4 v0, 0x0

    sget-object v1, Lcom/payegis/FirstApplication;->shellApp:Landroid/app/Application;

    invoke-virtual {v1}, Landroid/app/Application;->getBaseContext()Landroid/content/Context;

    move-result-object v1

    invoke-direct {p0, v0, v1}, Lcom/payegis/FirstApplication;->oncreate(Landroid/app/Application;Landroid/content/Context;)V

    goto :goto_1c
.end method

.method public showToast(Ljava/lang/String;)V
    .registers 3

    .prologue
    .line 183
    const/4 v0, 0x0

    invoke-static {p0, p1, v0}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;

    move-result-object v0

    invoke-virtual {v0}, Landroid/widget/Toast;->show()V

    .line 184
    return-void
.end method
